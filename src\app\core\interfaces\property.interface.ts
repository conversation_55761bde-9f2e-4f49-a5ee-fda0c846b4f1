import {
  EnquiryType,
  FurnishStatus,
  PropertyAttributeFieldType,
  PropertyStatus,
  SaleType,
} from 'src/app/app.enum';
import {
  Entity,
  PagedResponse,
} from 'src/app/core/interfaces/common.interface';

export interface PropertyResponse<Property> extends PagedResponse<Property> {
  items: Property[];
  data: Property;
}
export interface Property extends Entity {
  title: string;
  saleType: SaleType;
  enquiredFor: EnquiryType;
  notes: string;
  furnishStatus: FurnishStatus;
  status: PropertyStatus;
  rating: string;
  shareCount: number;
  possessionDate: string;
  micrositeURL: string;
  gOPropertyId: string;
  propertyType: PropertyTypeInfo;
  address: PropertyAddress;
  dimensionInfo: PropertyDimensionInfo;
  monetaryInfo: PropertyMonetaryInfo;
  ownerDetails: PropertyOwnerDetails;
  tagInfo: PropertyTagInfo;
  amenities: PropertyAmenity[];
  attributes: PropertyAttribute[];
  imageUrls: { [key: string]: string[] };
}
export interface PropertyAddress {
  id: string;
  placeId: string;
  subLocality: string;
  locality: string;
  district: string;
  city: string;
  state: string;
  country: string;
  postalCode: string;
  longitude: string;
  latitude: string;
  isGoogleMapLocation: boolean;
}
export interface PropertyAmenity {
  id: string;
  amenityName: string;
  amenityDisplayName: string;
  imageURL: string;
  amenityType: string;
  category: string;
}
export interface PropertyAttribute {
  id: string;
  attributeName: string;
  attributeDisplayName: string;
  attributeType: string;
  fieldType: PropertyAttributeFieldType;
  value: string;
}
export interface PropertyDimensionInfo {
  id: string;
  area: number;
  areaUnit: string;
  areaInSqMtr: number;
}

export interface PropertyDimension {
  id: string;
  unit: string;
  conversionFactor: number;
  isDeleted: boolean;
}

export interface PropertyMonetaryInfo {
  id: string;
  expectedPrice: number;
  isNegotiable: boolean;
  brokerage: string;
  brokerageUnit: string;
}
export interface PropertyOwnerDetails {
  id: string;
  name: string;
  phone: string;
  email: string;
}
export interface PropertyTagInfo {
  id: string;
  isFeatured: boolean;
}
export interface PropertyTypeInfo {
  id: string;
  displayName: string;
  noOfBHK: number;
  childType: MasterPropertyTypeChild;
}
export interface MasterPropertyTypeChild {
  id: string;
  displayName: string;
}

export interface PropertiesFilter {
  permission: any;
  UserIds: string;
  ListingOnBehalf: string;
  PossessionDate: any;
  PropertyTitle: string;
  OwnerNames: string;
  Facing: any;
  NoOfBalconies: number;
  NoOfKitchens: number;
  NoOfUtilites: number;
  NoOfBedrooms: number;
  NoOfLivingrooms: number;
  NoOfBathrooms: number;
  NoOfFloor: number;
  NoOfBHK: number;
  BHKs: any;
  FurnishStatuses: any;
  Projects: string;
  BHKTypes: string;
  pageNumber: number;
  pageSize: number;
  dateType?: any;
  fromDate?: string;
  toDate?: string;
  BasePropertyTypeId: string;
  PropertySubTypes: string;
  EnquiredFor?: any;
  PriceRange?: any;
  Locations: string;
  Cities: string;
  States: string;
  PropertySearch: string;
  path: string;
  'PropertySize.MinPropertyArea'?: number;
  'PropertySize.MaxPropertyArea'?: number;
  'PropertySize.PropertyAreaUnitId'?: string;
  'PropertySize.MinCarpetArea': number;
  'PropertySize.MaxCarpetArea': number;
  'PropertySize.CarpetAreaId': string;
  'PropertySize.MinBuildUpArea': number;
  'PropertySize.MaxBuildUpArea': number;
  'PropertySize.BuildUpAreaId': string;
  'PropertySize.MinSaleableArea': number;
  'PropertySize.MaxSaleableArea': number;
  'PropertySize.SaleableAreaId': string;
  MinPrice: number;
  MaxPrice: number;
  Currency: string,
  Amenities: any;
  PropertyStatus?: any;
  SerialNo: any;
  MinLeadCount: number;
  MaxLeadCount: number;
  MinProspectCount: number;
  MaxProspectCount: number;
  PossesionType: any;
  FromPossesionDate: any;
  ToPossesionDate: any;
}

export interface UserFilter {
  TimezoneIds: string;
  Departments: string;
  Designations: string;
  ReportsToIds: string;
  UserIds: string;
  pageNumber: number;
  pageSize: number;
  userSearch: string;
  path: string;
  userStatus: boolean;
  ShouldShowReportees: boolean;
  generalManagerIds?: string[];
  DateType?: any;
  FromDate?: string;
  ToDate?: string;
}

export interface BulkAssignProperty {
  userIds: string[];
  propertiesId: string[];
}
